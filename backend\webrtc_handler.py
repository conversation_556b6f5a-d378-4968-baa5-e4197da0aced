"""
WebRTC Handler for Real-time Voice Communication
Based on FastRTC implementation similar to AI Therapist
"""

import logging
import asyncio
import json
import base64
import time
import numpy as np
from typing import Dict, Any, Optional, AsyncGenerator
from datetime import datetime
from io import BytesIO
from PIL import Image
from config import settings

try:
    from fastrtc import Async<PERSON>treamHandler, AsyncAudioVideoStreamHandler, Stream
    from google import genai
    from google.genai.types import (
        LiveConnectConfig,
        PrebuiltVoiceConfig,
        SpeechConfig,
        VoiceConfig,
    )
    FASTRTC_AVAILABLE = True
except ImportError:
    FASTRTC_AVAILABLE = False
    # Fallback for when FastRTC is not available
    class AsyncAudioVideoStreamHandler:
        def __init__(self, expected_layout="mono", output_sample_rate=24000, input_sample_rate=16000):
            logger.warning("Using fallback AsyncAudioVideoStreamHandler")

        async def video_receive(self, frame):
            pass

        async def video_emit(self):
            return np.zeros((100, 100, 3), dtype=np.uint8)

from config import settings

logger = logging.getLogger(__name__)

# Session management
active_sessions = {}

# System prompt for Mai
SYSTEM_PROMPT = """You are Mai, a friendly and helpful AI assistant created by Critical Future.
You are warm, conversational, and speak naturally. Keep responses concise but engaging.
You're having a real-time voice conversation, so speak as if you're talking to a friend.
Your voice name is Aoede. When someone asks who you are, say you're Mai, created by Critical Future."""

def encode_image(data: np.ndarray) -> dict:
    """Encode image data to send to Gemini"""
    with BytesIO() as output_bytes:
        pil_image = Image.fromarray(data)
        pil_image.save(output_bytes, "JPEG")
        bytes_data = output_bytes.getvalue()
    base64_str = str(base64.b64encode(bytes_data), "utf-8")
    return {"mime_type": "image/jpeg", "data": base64_str}

def encode_audio_dict(data: np.ndarray) -> dict:
    """Encode audio data to send to Gemini"""
    return {
        "mime_type": "audio/pcm",
        "data": base64.b64encode(data.tobytes()).decode("UTF-8"),
    }

async def wait_for_item(queue, timeout):
    """Wait for an item from a queue with timeout"""
    try:
        return await asyncio.wait_for(queue.get(), timeout=timeout)
    except asyncio.TimeoutError:
        return None

class MaiVoiceHandler(AsyncStreamHandler):
    """Real-time voice handler using FastRTC and Gemini Live API"""

    def __init__(self):
        super().__init__()
        self.client = None
        self.session = None
        self.session_id = None
        self.quit = asyncio.Event()
        self.input_queue = asyncio.Queue(maxsize=100)
        self.output_queue = asyncio.Queue(maxsize=100)
        self.input_sample_rate = 16000
        self.output_sample_rate = 24000

    def copy(self) -> "MaiVoiceHandler":
        return MaiVoiceHandler()

    async def start_up(self):
        """Initialize Gemini Live session"""
        if not FASTRTC_AVAILABLE:
            logger.warning("FastRTC not available, skipping voice handler setup")
            return

        try:
            # Set default voice
            voice_name = "Aoede"

            # Validate API key first
            api_key = getattr(settings, 'gemini_api_key', None)
            if not api_key:
                logger.error("GEMINI_API_KEY not provided")
                return

            # Initialize Gemini client
            try:
                self.client = genai.Client(api_key=api_key)
                logger.info("Gemini client initialized successfully")
            except Exception as client_error:
                logger.error(f"Failed to initialize Gemini client: {client_error}")
                return

            # Create LiveConnectConfig
            try:
                config = LiveConnectConfig(
                    response_modalities=["AUDIO"],
                    speech_config=SpeechConfig(
                        voice_config=VoiceConfig(
                            prebuilt_voice_config=PrebuiltVoiceConfig(
                                voice_name=voice_name,
                            )
                        )
                    ),
                    generation_config={
                        "temperature": 0.8,
                        "max_output_tokens": 256,
                    },
                    system_instruction={
                        "parts": [{"text": SYSTEM_PROMPT}]
                    }
                )
                logger.info(f"LiveConnectConfig created with voice: {voice_name}")
            except Exception as config_error:
                logger.warning(f"Failed to create config with system_instruction: {config_error}")
                # Fallback config without system_instruction
                config = LiveConnectConfig(
                    response_modalities=["AUDIO"],
                    speech_config=SpeechConfig(
                        voice_config=VoiceConfig(
                            prebuilt_voice_config=PrebuiltVoiceConfig(
                                voice_name=voice_name,
                            )
                        )
                    ),
                    generation_config={
                        "temperature": 0.8,
                        "max_output_tokens": 256,
                    }
                )
                logger.info(f"Fallback LiveConnectConfig created with voice: {voice_name}")

            logger.info(f"Starting Gemini Live session with voice: {voice_name}")

            try:
                async with self.client.aio.live.connect(
                    model="gemini-2.0-flash-exp",
                    config=config
                ) as session:
                    logger.info("Gemini Live session established successfully")
                    self.session = session
                    self.session_id = f"voice_{int(time.time())}"
                    active_sessions[self.session_id] = {"type": "voice", "handler": self}

                    # System prompt will be handled by the model configuration
                    logger.info("Voice session ready - system prompt configured")

                    # Create tasks for sending and receiving audio
                    async def send_audio():
                        """Send audio from input queue to Gemini"""
                        while not self.quit.is_set():
                            try:
                                audio_data = await asyncio.wait_for(self.input_queue.get(), 0.1)
                                # Convert bytes to audio message format
                                audio_array = np.frombuffer(audio_data, dtype=np.int16)
                                audio_message = encode_audio_dict(audio_array)
                                await session.send(input=audio_message)
                            except asyncio.TimeoutError:
                                continue
                            except Exception as e:
                                logger.error(f"Error sending audio: {e}")
                                break

                    async def receive_audio():
                        """Receive audio responses from Gemini"""
                        while not self.quit.is_set():
                            try:
                                turn = session.receive()
                                async for chunk in turn:
                                    if self.quit.is_set():
                                        break

                                    if chunk.data:
                                        # Convert audio data to numpy array
                                        try:
                                            array = np.frombuffer(chunk.data, dtype=np.int16)
                                            if not self.quit.is_set() and array.size > 0:
                                                try:
                                                    self.output_queue.put_nowait((self.output_sample_rate, array))
                                                except asyncio.QueueFull:
                                                    logger.warning("Output queue full, dropping audio frame")
                                        except Exception as audio_error:
                                            logger.error(f"Error processing audio chunk: {audio_error}")

                                    if chunk.text:
                                        logger.info(f"Mai text response: {chunk.text[:100]}...")
                            except Exception as e:
                                if "ConnectionClosedOK" in str(e):
                                    logger.info("Gemini session closed normally")
                                    break
                                else:
                                    logger.error(f"Error in session loop: {e}")
                                    break

                    # Run both tasks concurrently
                    await asyncio.gather(
                        send_audio(),
                        receive_audio(),
                        return_exceptions=True
                    )

            except Exception as session_error:
                logger.error(f"Gemini Live session error: {session_error}")
                logger.info("Live session failed, handler will operate in degraded mode")
            finally:
                # Clean up session
                if self.session_id and self.session_id in active_sessions:
                    del active_sessions[self.session_id]

        except Exception as e:
            logger.error(f"Error in MaiVoiceHandler start_up: {e}")
            logger.info("Handler startup failed, will operate in degraded mode")
            await asyncio.sleep(1)



    async def receive(self, frame: tuple[int, np.ndarray]) -> None:
        """Receive audio from client and queue for Gemini"""
        try:
            if self.quit.is_set():
                return

            _, array = frame
            array = array.squeeze()

            # Validate audio data
            if array.size == 0:
                return

            # Encode audio for Gemini
            audio_bytes = array.astype(np.int16).tobytes()

            # Don't queue if we're shutting down
            if not self.quit.is_set():
                try:
                    self.input_queue.put_nowait(audio_bytes)
                except asyncio.QueueFull:
                    logger.warning("Input queue full, dropping audio frame")

        except Exception as e:
            logger.error(f"Error processing incoming audio: {e}")

    async def emit(self) -> tuple[int, np.ndarray] | None:
        """Emit audio response from Gemini to client"""
        try:
            if self.quit.is_set():
                return None
            return await asyncio.wait_for(self.output_queue.get(), timeout=0.1)
        except asyncio.TimeoutError:
            return None
        except Exception as e:
            logger.error(f"Error emitting audio: {e}")
            return None
class MaiVideoHandler(AsyncAudioVideoStreamHandler):
    """Video handler based on the working GeminiHandler code"""

    def __init__(self) -> None:
        super().__init__(
            "mono",
            output_sample_rate=24000,
            input_sample_rate=16000,
        )
        self.audio_queue = asyncio.Queue()
        self.video_queue = asyncio.Queue()
        self.session = None
        self.last_frame_time = 0
        self.quit = asyncio.Event()
        self.latest_args = [None, None, None]  # Initialize latest_args
        logger.info("🎥 MaiVideoHandler initialized")

    def copy(self) -> "MaiVideoHandler":
        """Create a copy of this handler for FastRTC"""
        new_handler = MaiVideoHandler()
        # Trigger startup manually for testing
        asyncio.create_task(new_handler.start_up())
        return new_handler

    async def wait_for_args(self):
        """Wait for arguments from FastRTC"""
        # Wait for latest_args to be set by FastRTC
        timeout = 0
        while self.latest_args[0] is None and timeout < 50:
            await asyncio.sleep(0.1)
            timeout += 1

    async def start_up(self):
        """Initialize Gemini Live session for video chat - based on working code"""
        logger.info("🎥 Starting video handler startup")
        client = genai.Client(
            api_key=settings.gemini_api_key,
            http_options={"api_version": "v1alpha"}
        )
        config = {"response_modalities": ["AUDIO"]}
        async with client.aio.live.connect(
            model="gemini-2.0-flash-exp",
            config=config,  # type: ignore
        ) as session:
            self.session = session
            logger.info("🎥 Video Gemini Live session established")
            while not self.quit.is_set():
                turn = self.session.receive()
                try:
                    async for response in turn:
                        if data := response.data:
                            audio = np.frombuffer(data, dtype=np.int16).reshape(1, -1)
                            logger.info(f"🎥 Received audio data, shape: {audio.shape}")
                            self.audio_queue.put_nowait(audio)
                        elif response.text:
                            logger.info(f"🎥 Received text response: {response.text}")
                        elif response.function_call:
                            logger.info(f"🎥 Received function call: {response.function_call}")
                        elif response.tool_call:
                            logger.info(f"🎥 Received tool call: {response.tool_call}")

                        # Keep the session alive by sending periodic pings
                        if not hasattr(self, '_last_ping') or time.time() - self._last_ping > 30:
                            await self.session.send(input={"text": "ping"})
                            self._last_ping = time.time()
                            logger.info("🎥 Sent ping to keep session alive")
                except Exception as e:
                    if "ConnectionClosedOK" in str(e) or "1000 (OK)" in str(e):
                        logger.info("🎥 Video connection closed normally")
                        break
                    else:
                        logger.error(f"🎥 Video session error: {e}")
                        # Try to reconnect instead of breaking immediately
                        logger.info("🎥 Attempting to maintain session...")
                        await asyncio.sleep(2)
                        continue

    async def video_receive(self, frame: np.ndarray):
        """Receive video frame from client - simplified and more stable"""
        if frame is None:
            return

        logger.info(f"🎥 Video frame received: {frame.shape}")
        self.video_queue.put_nowait(frame)

        if not self.session:
            logger.warning("🎥 No Gemini session available for video frame")
            return

        try:
            # Send image every 2 seconds to reduce load
            current_time = time.time()
            if current_time - self.last_frame_time > 2:
                self.last_frame_time = current_time
                logger.info("🎥 Sending video frame to Gemini")

                # Send the video frame
                await self.session.send(input=encode_image(frame))

                # Send a simple greeting only once to trigger audio response
                if not hasattr(self, '_greeting_sent'):
                    await self.session.send(input={"text": "Hi! I can see you. How can I help?"})
                    self._greeting_sent = True
                    logger.info("🎥 Initial greeting sent")

        except Exception as e:
            logger.error(f"🎥 Error processing video frame: {e}")

    async def video_emit(self):
        """Video emit - based on working code"""
        frame = await wait_for_item(self.video_queue, 0.01)
        if frame is not None:
            return frame
        else:
            return np.zeros((100, 100, 3), dtype=np.uint8)

    async def receive(self, frame: tuple[int, np.ndarray]) -> None:
        """Receive audio from client and send to Gemini - based on working code"""
        _, array = frame
        array = array.squeeze()
        logger.info(f"🎥 Audio received from client, shape: {array.shape}")
        audio_message = encode_audio_dict(array)
        if self.session:
            logger.info(f"🎥 Sending audio to Gemini Live session")
            await self.session.send(input=audio_message)
        else:
            logger.warning("🎥 No Gemini session available for audio")

    async def emit(self):
        """Emit audio response from Gemini to client - simplified and more stable"""
        try:
            array = await wait_for_item(self.audio_queue, 0.1)
            if array is not None:
                logger.info(f"🎥 Emitting audio array shape: {array.shape}")
                # Ensure audio is in the correct format
                if array.dtype != np.int16:
                    array = array.astype(np.int16)
                return (self.output_sample_rate, array)
            return None
        except Exception as e:
            logger.error(f"🎥 Error in emit: {e}")
            return None

    async def shutdown(self) -> None:
        """Clean shutdown of video handler - based on working code"""
        if self.session:
            self.quit.set()
            await self.session.close()
            self.quit.clear()

# Fallback functions for when FastRTC is not available
async def get_cloudflare_turn_credentials_async():
    return {
        "iceServers": [
            { "urls": ["stun:stun.l.google.com:19302"] },
            { "urls": ["stun:stun1.l.google.com:19302"] }
        ]
    }



if not FASTRTC_AVAILABLE:

    # Create fallback Stream class
    class Stream:
        def __init__(self, modality, mode, handler, rtc_configuration=None, **kwargs):
            self.modality = modality
            self.mode = mode
            self.handler = handler
            self.rtc_configuration = rtc_configuration

        def mount(self, app, path):
            logger.warning(f"FastRTC not available, mounting fallback stream at {path}")

            @app.post(f"{path}/webrtc/offer")
            async def webrtc_offer(request):
                return {
                    "status": "failed",
                    "meta": {
                        "error": "webrtc_not_available",
                        "message": "WebRTC features are not available",
                        "suggestion": "Use text chat mode instead"
                    }
                }

            @app.get(f"{path}/status")
            async def webrtc_status():
                return {
                    "available": False,
                    "error": "FastRTC not available",
                    "text_chat_available": True
                }

# Initialize Streams
try:
    if FASTRTC_AVAILABLE:
        # Audio-only stream
        audio_stream = Stream(
            modality="audio",
            mode="send-receive",
            handler=MaiVoiceHandler(),
            rtc_configuration=get_cloudflare_turn_credentials_async,
            concurrency_limit=5,
            time_limit=300,
        )

        # Video stream (user sends video, Mai responds with audio)
        video_stream = Stream(
            modality="audio-video",
            mode="send-receive",
            handler=MaiVideoHandler(),
            rtc_configuration=get_cloudflare_turn_credentials_async,
            concurrency_limit=3,
            time_limit=300,
        )

        logger.info("FastRTC Streams initialized successfully")
    else:
        # Fallback streams
        audio_stream = Stream(
            modality="audio",
            mode="send-receive",
            handler=MaiVoiceHandler(),
        )
        video_stream = Stream(
            modality="audio-video",
            mode="send-receive",
            handler=MaiVideoHandler(),
        )
        logger.warning("Using fallback Stream implementations")

except Exception as e:
    logger.error(f"Error initializing Streams: {e}")
    # Create minimal fallback streams
    audio_stream = Stream(
        modality="audio",
        mode="send-receive",
        handler=MaiVoiceHandler(),
    )
    video_stream = Stream(
        modality="audio-video",
        mode="send-receive",
        handler=MaiVideoHandler(),
    )







